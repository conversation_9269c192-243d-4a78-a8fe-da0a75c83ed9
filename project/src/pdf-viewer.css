/* PDF.js styles */
.react-pdf__Page {
  margin: 0 auto;
  display: block;
}

.react-pdf__Page__canvas {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  height: auto;
}

.react-pdf__Page__textContent {
  display: none;
}

.react-pdf__Page__annotations {
  display: none;
}

.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Loading spinner styles */
.react-pdf__message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
