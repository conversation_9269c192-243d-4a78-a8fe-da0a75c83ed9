import { PDFDocument } from '../types';

const API_BASE_URL = 'http://localhost:9000/api';

export class APIService {
  static async uploadPDF(file: File): Promise<PDFDocument> {
    const formData = new FormData();
    formData.append('pdf', file);

    const response = await fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to upload PDF');
    }

    const result = await response.json();
    return result.document;
  }

  static async getDocument(id: string): Promise<PDFDocument> {
    const response = await fetch(`${API_BASE_URL}/documents/${id}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to fetch document');
    }

    const result = await response.json();
    return result.document;
  }

  static async getAllDocuments(): Promise<PDFDocument[]> {
    const response = await fetch(`${API_BASE_URL}/documents`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to fetch documents');
    }

    const result = await response.json();
    return result.documents || [];
  }

  static async deleteDocument(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/documents/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to delete document');
    }
  }
}