import { useState, useEffect } from 'react';
import { PDFDocument } from '../types';
import { APIService } from '../services/api';

export const useDocuments = () => {
  const [documents, setDocuments] = useState<PDFDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const docs = await APIService.getAllDocuments();
      setDocuments(docs);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch documents');
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  const addDocument = (document: PDFDocument) => {
    setDocuments(prev => [document, ...prev]);
  };

  const removeDocument = async (id: string) => {
    try {
      await APIService.deleteDocument(id);
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to delete document');
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  return {
    documents,
    loading,
    error,
    addDocument,
    removeDocument,
    refetch: fetchDocuments
  };
};