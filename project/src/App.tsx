import React, { useState } from 'react';
import { Upload, FileText, Home, ArrowLeft } from 'lucide-react';
import { UploadArea } from './components/UploadArea';
import { PDFViewer } from './components/PDFViewer';
import { DocumentList } from './components/DocumentList';
import { Notification } from './components/Notification';
import { useDocuments } from './hooks/useDocuments';
import { PDFDocument } from './types';

type View = 'home' | 'upload' | 'viewer';

function App() {
  const [currentView, setCurrentView] = useState<View>('home');
  const [selectedDocument, setSelectedDocument] = useState<PDFDocument | null>(null);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const { documents, loading, addDocument, removeDocument } = useDocuments();

  const handleUploadSuccess = (document: PDFDocument) => {
    addDocument(document);
    setNotification({
      type: 'success',
      message: `${document.filename} uploaded successfully!`
    });
    setCurrentView('home');
  };

  const handleUploadError = (error: string) => {
    setNotification({
      type: 'error',
      message: error
    });
  };

  const handleDocumentSelect = (document: PDFDocument) => {
    setSelectedDocument(document);
    setCurrentView('viewer');
  };

  const handleDocumentDelete = async (id: string) => {
    try {
      await removeDocument(id);
      setNotification({
        type: 'success',
        message: 'Document deleted successfully'
      });

      if (selectedDocument?.id === id) {
        setSelectedDocument(null);
        setCurrentView('home');
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: 'Failed to delete document'
      });
    }
  };

  const renderHeader = () => (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <FileText size={20} className="text-white" />
            </div>
            <h1 className="text-xl font-semibold text-gray-900">
              PDF to HTML Converter
            </h1>
          </div>

          <nav className="flex items-center space-x-4">
            {currentView !== 'home' && (
              <button
                onClick={() => {
                  setCurrentView('home');
                  setSelectedDocument(null);
                }}
                className="flex items-center space-x-1 px-3 py-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              >
                <Home size={16} />
                <span className="hidden sm:inline">Home</span>
              </button>
            )}

            {currentView !== 'upload' && (
              <button
                onClick={() => setCurrentView('upload')}
                className="flex items-center space-x-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Upload size={16} />
                <span className="hidden sm:inline">Upload PDF</span>
              </button>
            )}

            {currentView === 'viewer' && selectedDocument && (
              <button
                onClick={() => setCurrentView('home')}
                className="flex items-center space-x-1 px-3 py-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back</span>
              </button>
            )}
          </nav>
        </div>
      </div>
    </header>
  );

  const renderContent = () => {
    if (loading && documents.length === 0) {
      return (
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-500 rounded-full"></div>
          <span className="ml-3 text-gray-600">Loading...</span>
        </div>
      );
    }

    switch (currentView) {
      case 'upload':
        return (
          <div className="max-w-4xl mx-auto py-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Upload Your PDF
              </h2>
              <p className="text-lg text-gray-600">
                View your PDF documents with page navigation
              </p>
            </div>
            <UploadArea
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          </div>
        );

      case 'viewer':
        return selectedDocument ? (
          <div className="py-6">
            <PDFViewer document={selectedDocument} />
          </div>
        ) : null;

      case 'home':
      default:
        return (
          <div className="max-w-7xl mx-auto py-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome to PDF Viewer
              </h2>
              <p className="text-gray-600">
                Upload and view PDF documents with page navigation
              </p>
            </div>

            <DocumentList
              documents={documents}
              onDocumentSelect={handleDocumentSelect}
              onDocumentDelete={handleDocumentDelete}
            />

            {documents.length === 0 && !loading && (
              <div className="text-center py-12">
                <div className="mb-6">
                  <div className="h-24 w-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Upload size={32} className="text-blue-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Ready to get started?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Upload your first PDF document to see the magic happen
                  </p>
                  <button
                    onClick={() => setCurrentView('upload')}
                    className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                  >
                    <Upload size={20} />
                    <span>Upload PDF</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {renderHeader()}

      <main className="px-4 sm:px-6 lg:px-8">
        {renderContent()}
      </main>

      {notification && (
        <Notification
          type={notification.type}
          message={notification.message}
          onClose={() => setNotification(null)}
        />
      )}
    </div>
  );
}

export default App;