import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Monitor, Smartphone, Tablet, ZoomIn, ZoomOut } from 'lucide-react';
import { PDFDocument } from '../types';

interface HTMLViewerProps {
  document: PDFDocument;
}

type ViewMode = 'desktop' | 'tablet' | 'mobile';

export const HTMLViewer: React.FC<HTMLViewerProps> = ({ document }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('desktop');
  const [currentPage, setCurrentPage] = useState(1);
  const [zoom, setZoom] = useState(100);

  const getViewportClass = () => {
    switch (viewMode) {
      case 'mobile':
        return 'w-80 h-[600px]';
      case 'tablet':
        return 'w-[768px] h-[900px]';
      case 'desktop':
      default:
        return 'w-full h-[800px]';
    }
  };

  const getViewportIcon = (mode: ViewMode) => {
    switch (mode) {
      case 'mobile':
        return <Smartphone size={20} />;
      case 'tablet':
        return <Tablet size={20} />;
      case 'desktop':
      default:
        return <Monitor size={20} />;
    }
  };

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));

  return (
    <div className="w-full space-y-6">
      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4 p-4 bg-white rounded-lg shadow-sm border">
        <div className="flex items-center space-x-2">
          <h2 className="text-lg font-semibold text-gray-800">
            {document.filename}
          </h2>
          <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
            {document.status}
          </span>
        </div>

        {/* Viewport Controls */}
        <div className="flex items-center space-x-2">
          {(['desktop', 'tablet', 'mobile'] as ViewMode[]).map((mode) => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={`
                flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors
                ${viewMode === mode 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              {getViewportIcon(mode)}
              <span className="capitalize text-sm">{mode}</span>
            </button>
          ))}
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleZoomOut}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <ZoomOut size={16} />
          </button>
          <span className="text-sm font-medium text-gray-600 min-w-[60px] text-center">
            {zoom}%
          </span>
          <button
            onClick={handleZoomIn}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <ZoomIn size={16} />
          </button>
        </div>
      </div>

      {/* Page Navigation */}
      {document.pageCount > 1 && (
        <div className="flex items-center justify-center space-x-4 p-4 bg-white rounded-lg shadow-sm border">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="flex items-center space-x-1 px-3 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeft size={16} />
            <span>Previous</span>
          </button>
          
          <span className="text-sm text-gray-600">
            Page {currentPage} of {document.pageCount}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, document.pageCount))}
            disabled={currentPage === document.pageCount}
            className="flex items-center space-x-1 px-3 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span>Next</span>
            <ChevronRight size={16} />
          </button>
        </div>
      )}

      {/* Viewer Container */}
      <div className="flex justify-center p-4 bg-gray-50 rounded-lg">
        <div 
          className={`
            ${getViewportClass()} 
            bg-white rounded-lg shadow-lg overflow-auto border
            transition-all duration-300
          `}
          style={{ transform: `scale(${zoom / 100})`, transformOrigin: 'top center' }}
        >
          <div 
            className="p-6 min-h-full"
            dangerouslySetInnerHTML={{ __html: document.htmlContent }}
          />
        </div>
      </div>
    </div>
  );
};