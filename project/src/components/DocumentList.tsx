import React from 'react';
import { FileText, Calendar, Trash2, Eye } from 'lucide-react';
import { PDFDocument } from '../types';

interface DocumentListProps {
  documents: PDFDocument[];
  onDocumentSelect: (document: PDFDocument) => void;
  onDocumentDelete: (id: string) => void;
}

export const DocumentList: React.FC<DocumentListProps> = ({ 
  documents, 
  onDocumentSelect, 
  onDocumentDelete 
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: PDFDocument['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'processing':
        return 'bg-yellow-100 text-yellow-700';
      case 'error':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">No documents yet</h3>
        <p className="text-gray-500">Upload your first PDF to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Documents</h2>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {documents.map((document) => (
          <div
            key={document.id}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow p-4"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FileText size={20} className="text-blue-500" />
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(document.status)}`}>
                  {document.status}
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => onDocumentSelect(document)}
                  className="p-1 rounded hover:bg-gray-100 text-gray-600 hover:text-blue-600 transition-colors"
                  title="View document"
                >
                  <Eye size={16} />
                </button>
                <button
                  onClick={() => onDocumentDelete(document.id)}
                  className="p-1 rounded hover:bg-gray-100 text-gray-600 hover:text-red-600 transition-colors"
                  title="Delete document"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
            
            <h3 className="font-medium text-gray-800 mb-2 truncate" title={document.filename}>
              {document.filename}
            </h3>
            
            <div className="flex items-center text-sm text-gray-500 space-x-4">
              <div className="flex items-center space-x-1">
                <Calendar size={14} />
                <span>{formatDate(document.uploadedAt)}</span>
              </div>
              <span>{document.pageCount} pages</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};