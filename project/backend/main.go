package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
)

type PDFDocument struct {
	ID          string `json:"id"`
	Filename    string `json:"filename"`
	UploadedAt  string `json:"uploadedAt"`
	HTMLContent string `json:"htmlContent"`
	PageCount   int    `json:"pageCount"`
	Status      string `json:"status"`
}

type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

var (
	documents = make(map[string]*PDFDocument)
	docMutex  = sync.RWMutex{}
)

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Max-Age", "86400")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func main() {
	// Create uploads directory if it doesn't exist
	if err := os.MkdirAll("uploads", 0755); err != nil {
		log.Fatal("Failed to create uploads directory:", err)
	}

	r := mux.NewRouter()

	// Apply CORS middleware to all routes
	r.Use(corsMiddleware)

	// API routes
	api := r.PathPrefix("/api").Subrouter()
	api.HandleFunc("/upload", handleUpload).Methods("POST", "OPTIONS")
	api.HandleFunc("/documents", handleGetAllDocuments).Methods("GET", "OPTIONS")
	api.HandleFunc("/documents/{id}", handleGetDocument).Methods("GET", "OPTIONS")
	api.HandleFunc("/documents/{id}", handleDeleteDocument).Methods("DELETE", "OPTIONS")

	fmt.Println("Server starting on :8080")
	fmt.Println("CORS enabled for all origins")
	log.Fatal(http.ListenAndServe(":8080", r))
}

func handleUpload(w http.ResponseWriter, r *http.Request) {
	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Parse multipart form
	err := r.ParseMultipartForm(10 << 20) // 10MB max
	if err != nil {
		respondWithError(w, "File too large or invalid form data", http.StatusBadRequest)
		return
	}

	file, header, err := r.FormFile("pdf")
	if err != nil {
		respondWithError(w, "No file provided or invalid file field", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file type
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".pdf") {
		respondWithError(w, "Only PDF files are allowed", http.StatusBadRequest)
		return
	}

	// Generate unique ID
	docID := uuid.New().String()

	// Save uploaded file
	uploadPath := filepath.Join("uploads", docID+".pdf")
	dst, err := os.Create(uploadPath)
	if err != nil {
		respondWithError(w, "Failed to save file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	_, err = io.Copy(dst, file)
	if err != nil {
		respondWithError(w, "Failed to save file", http.StatusInternalServerError)
		return
	}

	// Create document record
	doc := &PDFDocument{
		ID:         docID,
		Filename:   header.Filename,
		UploadedAt: time.Now().Format(time.RFC3339),
		Status:     "processing",
		PageCount:  1, // Will be updated after conversion
	}

	// Store document
	docMutex.Lock()
	documents[docID] = doc
	docMutex.Unlock()

	// Convert PDF to HTML in background
	go convertPDFToHTML(docID, uploadPath)

	respondWithJSON(w, map[string]*PDFDocument{"document": doc})
}

func convertPDFToHTML(docID, filePath string) {
	outputDir := filepath.Join("converted", docID)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Println("Error creating output directory:", err)
		updateStatus(docID, "error", 0, "")
		return
	}

	// Use Poppler's pdftohtml
	htmlFilePath := filepath.Join(outputDir, "output.html")
	log.Printf("htmlFilePath htmlFilePath htmlFilePath ", htmlFilePath)
	cmd := exec.Command("pdftohtml", "-c", "-noframes", "-stdout", filePath)
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		log.Printf("Error converting PDF to HTML: %v\n%s", err, out.String())
		updateStatus(docID, "error", 0, "")
		return
	}

	htmlContent := out.String()
	pageCount := countPages(htmlContent)

	docMutex.Lock()
	doc, exists := documents[docID]
	if exists {
		doc.HTMLContent = htmlContent
		doc.Status = "completed"
		doc.PageCount = pageCount
	}
	docMutex.Unlock()
}

func updateStatus(docID, status string, pageCount int, html string) {
	docMutex.Lock()
	defer docMutex.Unlock()

	if doc, exists := documents[docID]; exists {
		doc.Status = status
		doc.PageCount = pageCount
		doc.HTMLContent = html
	}
}

func generateSampleHTML(filename string) string {
	return fmt.Sprintf(`
		<div style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.8; padding: 40px; max-width: 800px; margin: 0 auto; color: #333;">
			<div style="text-align: center; margin-bottom: 40px; border-bottom: 3px solid #3B82F6; padding-bottom: 20px;">
				<h1 style="color: #1E40AF; margin-bottom: 10px; font-size: 2.5em; font-weight: 700;">%s</h1>
				<p style="color: #6B7280; font-size: 1.1em; margin: 0;">Converted PDF Document</p>
			</div>

			<div style="background: linear-gradient(135deg, #EBF4FF 0%%, #DBEAFE 100%%); padding: 30px; border-radius: 12px; margin-bottom: 30px;">
				<h2 style="color: #1E40AF; margin-top: 0; margin-bottom: 20px; font-size: 1.8em;">Document Overview</h2>
				<p style="margin-bottom: 20px; font-size: 1.1em;">This PDF document has been successfully converted to responsive HTML format. The conversion process preserves the original formatting while making the content accessible across all devices.</p>
				
				<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 25px;">
					<div style="background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
						<div style="color: #10B981; font-size: 2em; margin-bottom: 10px;">✓</div>
						<h3 style="margin: 0; color: #374151; font-size: 1.1em;">Fully Responsive</h3>
					</div>
					<div style="background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
						<div style="color: #8B5CF6; font-size: 2em; margin-bottom: 10px;">📱</div>
						<h3 style="margin: 0; color: #374151; font-size: 1.1em;">Mobile Friendly</h3>
					</div>
					<div style="background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
						<div style="color: #F59E0B; font-size: 2em; margin-bottom: 10px;">⚡</div>
						<h3 style="margin: 0; color: #374151; font-size: 1.1em;">Fast Loading</h3>
					</div>
				</div>
			</div>

			<section style="margin-bottom: 40px;">
				<h2 style="color: #1E40AF; margin-bottom: 20px; font-size: 1.8em; border-left: 4px solid #3B82F6; padding-left: 15px;">Key Features</h2>
				<div style="background: #F9FAFB; padding: 25px; border-radius: 8px;">
					<ul style="list-style: none; padding: 0; margin: 0;">
						<li style="margin-bottom: 15px; padding-left: 30px; position: relative;">
							<span style="position: absolute; left: 0; color: #10B981; font-weight: bold;">•</span>
							<strong>Multi-Device Responsiveness:</strong> Optimized viewing experience across desktop, tablet, and mobile devices
						</li>
						<li style="margin-bottom: 15px; padding-left: 30px; position: relative;">
							<span style="position: absolute; left: 0; color: #10B981; font-weight: bold;">•</span>
							<strong>Preserved Formatting:</strong> Maintains original document structure and styling
						</li>
						<li style="margin-bottom: 15px; padding-left: 30px; position: relative;">
							<span style="position: absolute; left: 0; color: #10B981; font-weight: bold;">•</span>
							<strong>Interactive Navigation:</strong> Easy-to-use controls for multi-page documents
						</li>
						<li style="margin-bottom: 15px; padding-left: 30px; position: relative;">
							<span style="position: absolute; left: 0; color: #10B981; font-weight: bold;">•</span>
							<strong>Zoom Capabilities:</strong> Adjustable zoom levels for better readability
						</li>
						<li style="margin-bottom: 0; padding-left: 30px; position: relative;">
							<span style="position: absolute; left: 0; color: #10B981; font-weight: bold;">•</span>
							<strong>Cross-Platform Compatibility:</strong> Works seamlessly on all modern browsers
						</li>
					</ul>
				</div>
			</section>

			<section style="margin-bottom: 40px;">
				<h2 style="color: #1E40AF; margin-bottom: 20px; font-size: 1.8em; border-left: 4px solid #3B82F6; padding-left: 15px;">Technical Specifications</h2>
				<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
					<div style="background: #FEF3C7; padding: 20px; border-radius: 8px; border-left: 4px solid #F59E0B;">
						<h3 style="margin-top: 0; color: #92400E;">Processing Engine</h3>
						<p style="margin-bottom: 0; color: #78350F;">Advanced PDF parsing with Go backend</p>
					</div>
					<div style="background: #DBEAFE; padding: 20px; border-radius: 8px; border-left: 4px solid #3B82F6;">
						<h3 style="margin-top: 0; color: #1E40AF;">Frontend Framework</h3>
						<p style="margin-bottom: 0; color: #1E3A8A;">React with TypeScript and Tailwind CSS</p>
					</div>
				</div>
			</section>

			<section style="background: #F3F4F6; padding: 30px; border-radius: 12px; text-align: center;">
				<h2 style="color: #374151; margin-top: 0; margin-bottom: 15px;">Ready for Production</h2>
				<p style="color: #6B7280; margin-bottom: 0; font-size: 1.1em;">This converted document demonstrates the full capabilities of the PDF to HTML conversion system, showcasing responsive design and professional formatting.</p>
			</section>
		</div>`, filename)
}

func countPages(htmlContent string) int {
	// Simple page counting based on content length
	// In a real implementation, this would be more sophisticated
	if len(htmlContent) > 2000 {
		return 2
	}
	return 1
}

func handleGetAllDocuments(w http.ResponseWriter, r *http.Request) {
	docMutex.RLock()
	defer docMutex.RUnlock()

	docs := make([]*PDFDocument, 0, len(documents))
	for _, doc := range documents {
		docs = append(docs, doc)
	}

	respondWithJSON(w, map[string][]*PDFDocument{"documents": docs})
}

func handleGetDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	docMutex.RLock()
	doc, exists := documents[id]
	docMutex.RUnlock()

	if !exists {
		respondWithError(w, "Document not found", http.StatusNotFound)
		return
	}

	respondWithJSON(w, map[string]*PDFDocument{"document": doc})
}

func handleDeleteDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	docMutex.Lock()
	defer docMutex.Unlock()

	_, exists := documents[id]
	if !exists {
		respondWithError(w, "Document not found", http.StatusNotFound)
		return
	}

	// Delete file from filesystem
	filePath := filepath.Join("uploads", id+".pdf")
	os.Remove(filePath)

	// Remove from memory
	delete(documents, id)

	w.WriteHeader(http.StatusNoContent)
}

func respondWithJSON(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(data)
}

func respondWithError(w http.ResponseWriter, message string, code int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(APIResponse{
		Success: false,
		Error:   message,
	})
}
