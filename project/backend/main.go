package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
)

type PDFDocument struct {
	ID         string `json:"id"`
	Filename   string `json:"filename"`
	UploadedAt string `json:"uploadedAt"`
	PdfUrl     string `json:"pdfUrl"`
	PageCount  int    `json:"pageCount"`
	Status     string `json:"status"`
}

type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

var (
	documents = make(map[string]*PDFDocument)
	docMutex  = sync.RWMutex{}
)

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Max-Age", "86400")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func main() {
	// Create uploads directory if it doesn't exist
	if err := os.MkdirAll("uploads", 0755); err != nil {
		log.Fatal("Failed to create uploads directory:", err)
	}

	r := mux.NewRouter()

	// Apply CORS middleware to all routes
	r.Use(corsMiddleware)

	// API routes
	api := r.PathPrefix("/api").Subrouter()
	api.HandleFunc("/upload", handleUpload).Methods("POST", "OPTIONS")
	api.HandleFunc("/documents", handleGetAllDocuments).Methods("GET", "OPTIONS")
	api.HandleFunc("/documents/{id}", handleGetDocument).Methods("GET", "OPTIONS")
	api.HandleFunc("/documents/{id}", handleDeleteDocument).Methods("DELETE", "OPTIONS")

	// Serve PDF files with CORS
	r.PathPrefix("/uploads/").Handler(corsMiddleware(http.StripPrefix("/uploads/", http.FileServer(http.Dir("uploads/")))))

	fmt.Println("Server starting on :9000")
	fmt.Println("CORS enabled for all origins")
	log.Fatal(http.ListenAndServe(":9000", r))
}

func handleUpload(w http.ResponseWriter, r *http.Request) {
	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Parse multipart form
	err := r.ParseMultipartForm(10 << 20) // 10MB max
	if err != nil {
		respondWithError(w, "File too large or invalid form data", http.StatusBadRequest)
		return
	}

	file, header, err := r.FormFile("pdf")
	if err != nil {
		respondWithError(w, "No file provided or invalid file field", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file type
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".pdf") {
		respondWithError(w, "Only PDF files are allowed", http.StatusBadRequest)
		return
	}

	// Generate unique ID
	docID := uuid.New().String()

	// Save uploaded file
	uploadPath := filepath.Join("uploads", docID+".pdf")
	dst, err := os.Create(uploadPath)
	if err != nil {
		respondWithError(w, "Failed to save file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	_, err = io.Copy(dst, file)
	if err != nil {
		respondWithError(w, "Failed to save file", http.StatusInternalServerError)
		return
	}

	// Create document record
	doc := &PDFDocument{
		ID:         docID,
		Filename:   header.Filename,
		UploadedAt: time.Now().Format(time.RFC3339),
		PdfUrl:     fmt.Sprintf("http://localhost:9000/uploads/%s.pdf", docID),
		Status:     "processing",
		PageCount:  1, // Will be updated after getting PDF info
	}

	// Store document
	docMutex.Lock()
	documents[docID] = doc
	docMutex.Unlock()

	// Get PDF info in background
	go getPDFInfo(docID, uploadPath)

	respondWithJSON(w, map[string]*PDFDocument{"document": doc})
}

func getPDFInfo(docID, filePath string) {
	// Use pdfinfo to get page count
	cmd := exec.Command("pdfinfo", filePath)
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		log.Printf("Error getting PDF info: %v\n%s", err, out.String())
		updateStatus(docID, "error", 1)
		return
	}

	// Parse page count from pdfinfo output
	pageCount := 1 // default
	output := out.String()
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "Pages:") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				if count, err := strconv.Atoi(parts[1]); err == nil {
					pageCount = count
				}
			}
			break
		}
	}

	docMutex.Lock()
	doc, exists := documents[docID]
	if exists {
		doc.Status = "completed"
		doc.PageCount = pageCount
	}
	docMutex.Unlock()
}

func updateStatus(docID, status string, pageCount int) {
	docMutex.Lock()
	defer docMutex.Unlock()

	if doc, exists := documents[docID]; exists {
		doc.Status = status
		doc.PageCount = pageCount
	}
}

func handleGetAllDocuments(w http.ResponseWriter, r *http.Request) {
	docMutex.RLock()
	defer docMutex.RUnlock()

	docs := make([]*PDFDocument, 0, len(documents))
	for _, doc := range documents {
		docs = append(docs, doc)
	}

	respondWithJSON(w, map[string][]*PDFDocument{"documents": docs})
}

func handleGetDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	docMutex.RLock()
	doc, exists := documents[id]
	docMutex.RUnlock()

	if !exists {
		respondWithError(w, "Document not found", http.StatusNotFound)
		return
	}

	respondWithJSON(w, map[string]*PDFDocument{"document": doc})
}

func handleDeleteDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	docMutex.Lock()
	defer docMutex.Unlock()

	_, exists := documents[id]
	if !exists {
		respondWithError(w, "Document not found", http.StatusNotFound)
		return
	}

	// Delete file from filesystem
	filePath := filepath.Join("uploads", id+".pdf")
	os.Remove(filePath)

	// Remove from memory
	delete(documents, id)

	w.WriteHeader(http.StatusNoContent)
}

func respondWithJSON(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(data)
}

func respondWithError(w http.ResponseWriter, message string, code int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(APIResponse{
		Success: false,
		Error:   message,
	})
}
