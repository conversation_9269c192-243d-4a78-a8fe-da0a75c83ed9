# PDF to HTML Converter

A modern web application that converts PDF documents to responsive HTML format, built with React frontend and Go backend.

## Features

- **PDF Upload**: Drag & drop or click to upload PDF files
- **Real-time Conversion**: Server-side PDF to HTML conversion
- **Responsive Viewer**: View converted documents across different screen sizes
- **Multi-Device Preview**: Desktop, tablet, and mobile viewport modes
- **Document Management**: Store, view, and delete converted documents
- **Zoom Controls**: Adjustable zoom levels for better readability
- **Progress Tracking**: Real-time upload and conversion progress

## Tech Stack

### Frontend
- React 18 with TypeScript  
- Tailwind CSS for styling
- Lucide React for icons
- Vite for development and building

### Backend
- Go with Gorilla Mux for routing
- CORS support for cross-origin requests
- File upload handling
- In-memory document storage

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Go (v1.21 or higher)

### Installation

1. Install frontend dependencies:
```bash
npm install
```

2. Install Go dependencies:
```bash
cd backend
go mod tidy
```

### Running the Application

1. Start the Go backend server:
```bash
npm run backend
```
The backend will start on http://localhost:8080

2. In a new terminal, start the React frontend:
```bash
npm run dev
```
The frontend will start on http://localhost:5173

### Usage

1. Open http://localhost:5173 in your browser
2. Click "Upload PDF" or drag & drop a PDF file
3. Wait for the conversion to complete
4. View the converted HTML document with responsive controls
5. Switch between desktop, tablet, and mobile preview modes
6. Use zoom controls for better readability

## API Endpoints

- `POST /api/upload` - Upload a PDF file
- `GET /api/documents` - Get all documents
- `GET /api/documents/{id}` - Get a specific document
- `DELETE /api/documents/{id}` - Delete a document

## Project Structure

```
├── src/
│   ├── components/          # React components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API service layer
│   ├── types/              # TypeScript type definitions
│   └── ...
├── backend/
│   ├── main.go             # Go backend server
│   └── ...
└── ...
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.